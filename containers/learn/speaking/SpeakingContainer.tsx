'use client';
import React from 'react';
import classNames from 'classnames';
import ScrollArea from '@/components/ScrollArea';
import { SPECIAL_DOCUMENT_ID } from '@/configs';
import FinishLearnControl from '@/containers/learn/FinishScreen';
import SpeakingItemLeft from '@/containers/learn/speaking/SpeakingItemLeft';
import SpeakingItemRight from '@/containers/learn/speaking/SpeakingItemRight';
import CountDownSpeaking from '@/containers/learn/speaking/CountDownSpeaking';
import { useSpeakingManager } from '@/hooks/Ent/useSpeakingManager';
import StartScreen from '@/containers/learn/StartScreen';
import { groupConversationSentences } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import SpeakingBottomBar from '@/containers/learn/speaking/SpeakingBottomBar';
import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';
import BackActionEnum from '@/configs/ConversationEnum';

const SpeakingContainer = () => {
  // Context and other hooks
  const { characters, sentences, activeTab, paragraph, isLoadingConversations } = useConversationContext();
  const conversations = groupConversationSentences(sentences);

  const {
    speakings,
    isFinishLearn,
    activeCharacter,
    exerciseToken,
    isPauseRecord,
    isPauseSpeak,
    forwardMode,
    showCountDown,
    showTooltipRecord,
    sentenceProcess,
    isSendingRecord,
    isStartLearn,
    isStartMicrophone,
    transcript,
    ref,
    handeBackSentence,
    handleStartLearn,
    handleFinishCountDown,
    toggleRecording,
    setIsPauseSpeak,
    setCharacter,
    stopMicrophone,
  } = useSpeakingManager({
    conversations,
    paragraph
  });

  if (isLoadingConversations) {
    return (
      <div
        className={
          'w-full px-[calc((100%_-_615px)_/_2)] !overflow-y-scroll flex flex-col gap-5 py-5'
        }
      >
        {map(range(1, 12), (index) => (
          <div key={index} className={'w-full flex items-end gap-3'}>
            <Skeleton className="rounded-full shrink h-10 w-10" />
            <div className={'w-full flex flex-1 items-start flex-col gap-2'}>
              <Skeleton className={'rounded-sm w-1/5 h-2'} />
              <Skeleton className={'rounded-sm w-2/5 h-4'} />
              <Skeleton className={'rounded-sm w-3/5 h-4'} />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isStartLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartLearn}
        setSelectedCharacter={setCharacter}
        selectedCharacter={activeCharacter || null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinishLearn) {
    return (
      <FinishLearnControl
        handleRestart={() => handeBackSentence(BackActionEnum.START)}
        handleDoNewExercise={function (): void {
          throw new Error('Function not implemented.');
        }}
        isExercise={false}
      />
    );
  }

  return (
    <>
      <div className="mb-[70px] justify-between flex flex-col pl-8 h-full w-full border-b border-color-line">
        <ScrollArea
          className={classNames('w-full px-[calc((100%_-_615px)_/_2)]', {
            'h-[calc(100vh-170px)]':
              paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken === '',
            'h-[calc(100vh-129px)]':
              paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken !== '',
            'h-[calc(100vh-121px)]':
              paragraph?.document_id === SPECIAL_DOCUMENT_ID &&
              characters.length &&
              exerciseToken === '',
            'h-[calc(100vh-80px)]':
              paragraph?.document_id === SPECIAL_DOCUMENT_ID &&
              characters.length &&
              exerciseToken !== '',
          })}
        >
          <div
            className={classNames('pt-8 pr-8 flex flex-col font-[Helvetica] font-normal space-y-4')}
          >
            {showCountDown ? (
              <CountDownSpeaking onFinishCountDown={handleFinishCountDown} />
            ) : (
              activeCharacter &&
              speakings?.map((speakingItems, index) =>
                activeCharacter.id !== speakingItems[0].character_id ? (
                  <SpeakingItemLeft
                    characters={characters}
                    key={`${index}-left`}
                    sentences={speakingItems}
                    isLastItem={speakings.length - 1 === index}
                    isPause={isPauseSpeak}
                    className={classNames({
                      'opacity-80': speakings.length - 3 === index,
                      'opacity-60': speakings.length - 2 === index,
                      'opacity-10': speakings.length - 1 === index,
                    })}
                    currentGroupIndex={index}
                  />
                ) : (
                  <SpeakingItemRight
                    key={`${index}-right`}
                    isLastItem={speakings.length - 1 === index}
                    activeCharacter={activeCharacter}
                    sentences={speakingItems}
                    characters={characters}
                    forwardMode={forwardMode}
                    className={classNames({
                      'opacity-80': speakings.length - 3 === index,
                      'opacity-60': speakings.length - 2 === index,
                      'opacity-10': speakings.length - 1 === index,
                    })}
                    currentGroupIndex={index}
                  />
                )
              )
            )}
            <div ref={ref}></div>
          </div>
        </ScrollArea>
      </div>

      <SpeakingBottomBar
        handleStartLearn={handleStartLearn}
        handeBackSentence={handeBackSentence}
        toggleRecording={toggleRecording}
        setIsPauseSpeak={setIsPauseSpeak}
        showTooltipRecord={showTooltipRecord}
        isPauseSpeak={isPauseSpeak}
        isSendingRecord={isSendingRecord}
        sentenceProcess={sentenceProcess}
        isPauseRecord={isPauseRecord}
        isStartMicrophone={isStartMicrophone}
        speakings={speakings}
        isFinishLearn={isFinishLearn}
        activeCharacter={activeCharacter}
        stopMicrophone={stopMicrophone}
        transcript={transcript}
      />
    </>
  );
};

export default SpeakingContainer;
