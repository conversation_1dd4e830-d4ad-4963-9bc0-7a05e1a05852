'use client';
import VolumeControl from 'containers/learn/VolumeControl';
import React from 'react';
import { SpeakingControlProps } from 'types/component';
import SentencesControl from '@/containers/learn/speaking/SpeakingControl/SentencesControl';
import SubmitAction from '@/containers/learn/speaking/SpeakingControl/SubmitAction';

const SpeakingControl = ({
  showTooltipRecord = false,
  setIsPauseSpeak,
  toggleRecording,
  handeBackSentence,
  stopMicrophone,
  isPauseSpeak,
  isSendingRecord,
  isPaused,
  isStartMicrophone,
  transcript,
}: SpeakingControlProps) => {


  return (
    <div className={'flex items-center justify-between w-full px-[calc((100%_-_615px)_/_2)]'}>
      <VolumeControl />
      <SentencesControl isPaused={isPaused}
        isPauseSpeak={isPauseSpeak}
        toggleRecording={toggleRecording}
        handeBackSentence={handeBackSentence}
        isSendingRecord={isSendingRecord}
        showTooltipRecord={showTooltipRecord}
        setIsPauseSpeak={setIsPauseSpeak}
        isStartMicrophone={isStartMicrophone}
        transcript={transcript}
      />

      <SubmitAction isSendingRecord={isSendingRecord} stopMicrophone={stopMicrophone} />
    </div>
  );
};
export default SpeakingControl;
