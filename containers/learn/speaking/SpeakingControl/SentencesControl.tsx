import classNames from 'classnames';
import { SentenceProcessEnum } from '@/configs/SentenceProcessEnum';
import BackActionEnum from '@/configs/ConversationEnum';
import { Tooltip } from '@heroui/react';
import { StatusEnum } from '@/configs/StatusEnum';
import React, { MouseEvent } from 'react';
import useSpeakingStore from '@/store/speaking';
import useLearnStore from '@/store/learn';
import useBalanceStore from '@/store/balance';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import TranscriptPopper from '@/components/TranscriptPopper';

const SentencesControl = ({ transcript, handeBackSentence, isPaused, isSendingRecord, toggleRecording, setIsPauseSpeak, showTooltipRecord, isPauseSpeak, isStartMicrophone }) => {
  const { sentence, sentenceProcess, recordProcess } = useSpeakingStore();
  const { activeCharacter } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const togglePauseOrPlay = (event: MouseEvent) => {
    if (balanceStatus != StatusEnum.ON) return;
    event.preventDefault();
    if (sentence?.character_id !== activeCharacter?.id) {
      setIsPauseSpeak(!isPauseSpeak);
    } else {
      toggleRecording();
    }
  };

  return (
    <div>
      <div className={'flex items-center'}>
        <div
          className={classNames('cursor-pointer mr-4', {
            'opacity-50':
              (sentenceProcess === SentenceProcessEnum.PROCESS &&
                activeCharacter?.id !== sentence?.character_id) ||
              (recordProcess === RecordProcessEnum.PROCESS && !isPaused),
          })}
          onClick={() => handeBackSentence(BackActionEnum.START)}
        >
          <i
            className={classNames('icon-start text-base text-color-minor', {
              'hover:text-purple':
                (sentenceProcess !== SentenceProcessEnum.PROCESS &&
                  activeCharacter?.id !== sentence?.character_id) ||
                isPaused ||
                recordProcess !== RecordProcessEnum.PROCESS,
            })}
          />
        </div>
        <div
          className={classNames('cursor-pointer mr-5', {
            'opacity-50':
              (sentenceProcess === SentenceProcessEnum.PROCESS &&
                activeCharacter?.id !== sentence?.character_id) ||
              (recordProcess === RecordProcessEnum.PROCESS && !isPaused),
          })}
          onClick={() => handeBackSentence(BackActionEnum.PREV)}
        >
          <i
            className={classNames('icon-play-back text-large text-color-minor', {
              'hover:text-purple':
                (sentenceProcess !== SentenceProcessEnum.PROCESS &&
                  activeCharacter?.id !== sentence?.character_id) ||
                isPaused ||
                recordProcess !== RecordProcessEnum.PROCESS,
            })}
          />
        </div>

        <div
          onClick={togglePauseOrPlay}
          className={classNames(
            'rounded-full relative cursor-pointer w-[50px] h-[50px] flex items-center flex-col justify-center',
          )}
        >
          {/* Transcript Popper */}
          <TranscriptPopper
            isVisible={isStartMicrophone && activeCharacter?.id === sentence?.character_id}
            transcript={transcript}
            template={sentence?.content}
          />

          <Tooltip
            showArrow={true}
            color={'secondary'}
            content={`Hãy nói câu "${sentence?.content}"`}
            placement={'top'}
            isOpen={showTooltipRecord}
          >
            <div
              className={classNames('bg-purple w-[50px] h-[50px] rounded-full', {
                'opacity-40':
                  recordProcess !== RecordProcessEnum.PROCESS &&
                  activeCharacter?.id === sentence?.character_id,
              })}
            >
              {isSendingRecord ? (
                <div className={'absolute  left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2'}>
                  <i className={'animate-spin text-white text-2xl icon-spin2'} />
                </div>
              ) : (
                <>
                  <i
                    className={classNames(
                      'text-2xl text-white absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
                      {
                        'icon-mic':
                          (isPaused || recordProcess !== RecordProcessEnum.PROCESS) &&
                          activeCharacter?.id === sentence?.character_id,
                        'icon-stop':
                          (!isPaused &&
                            recordProcess === RecordProcessEnum.PROCESS &&
                            activeCharacter?.id === sentence?.character_id) ||
                          (!isPauseSpeak && activeCharacter?.id !== sentence?.character_id),
                        'icon-play':
                          isPauseSpeak && activeCharacter?.id !== sentence?.character_id,
                      },
                    )}
                  />
                </>
              )}
            </div>
          </Tooltip>
          <div
            className={classNames('bg-purple hidden w-[50px] h-[50px] rounded-full ', {
              'animate-ping absolute !inline-flex opacity-75':
                !isPaused &&
                !isSendingRecord &&
                isStartMicrophone &&
                recordProcess === RecordProcessEnum.PROCESS &&
                balanceStatus === StatusEnum.ON,
            })}
          ></div>
          <span className={'absolute -bottom-3.5 text-[0.625rem]'}>Space</span>
        </div>
      </div>
    </div>
  );
};
export default SentencesControl;
